"""
Delivery Bot for Wiz Aroma Delivery System
Provides limited order details to delivery personnel for order acceptance.
Access restricted to authorized delivery personnel Telegram IDs.
"""

import telebot
from telebot import types
import datetime
import time
from typing import Dict, List, Any, Optional
import logging

from src.config import (
    DELIVERY_BOT_TOKEN,
    logger
)
try:
    from src.config import DELIVERY_BOT_AUTHORIZED_IDS
except ImportError:
    DELIVERY_BOT_AUTHORIZED_IDS = [7729984017]  # Fallback admin ID
from src.data_storage import (
    load_awaiting_receipt,
    load_order_status,
    get_restaurant_by_id,
    get_area_by_id
)
from src.firebase_db import get_data
from src.data_models import (
    awaiting_receipt,
    order_status,
    delivery_personnel_assignments
)
from src.utils.delivery_personnel_utils import (
    get_delivery_assignment_by_order,
    get_delivery_personnel_by_telegram_id,
    update_assignment_status,
    get_assignments_for_personnel
)


def format_delivery_location_for_bot(delivery_location, delivery_gate=None):
    """Format delivery location for delivery bot display, handling N/A values"""
    # The actual delivery address is often stored in delivery_gate field
    # Try delivery_gate first, then delivery_location as fallback
    actual_location = None

    if delivery_gate and delivery_gate != 'N/A' and delivery_gate.strip():
        actual_location = delivery_gate.strip()
    elif delivery_location and delivery_location != 'N/A' and delivery_location.strip():
        actual_location = delivery_location.strip()

    if not actual_location:
        return 'Location not specified'

    return actual_location


def get_proper_restaurant_name_for_bot(restaurant_id, fallback_name="Unknown Restaurant"):
    """Get proper restaurant name from database for delivery bot"""
    try:
        from src.data_storage import get_restaurant_by_id
        restaurant = get_restaurant_by_id(restaurant_id)
        if restaurant and restaurant.get('name'):
            return restaurant['name']
    except Exception as e:
        logger.error(f"Error getting restaurant name for ID {restaurant_id}: {e}")

    return fallback_name


def get_restaurant_phone_for_delivery():
    """Get the standard restaurant phone number for delivery notifications"""
    return "0909782606"


def format_privacy_protected_order_info(order_number, order_data, restaurant_name, items_text):
    """Format order info for delivery bot with privacy protections"""
    # Use restaurant phone instead of customer phone for privacy
    restaurant_phone = get_restaurant_phone_for_delivery()

    # Get proper restaurant name
    restaurant_id = order_data.get('restaurant_id')
    proper_restaurant_name = get_proper_restaurant_name_for_bot(restaurant_id, restaurant_name)

    # Format delivery location properly
    delivery_location = format_delivery_location_for_bot(
        order_data.get('delivery_location', 'N/A'),
        order_data.get('delivery_gate')
    )

    order_info = f"""📋 **Order #{order_number}**
🏪 Restaurant: {proper_restaurant_name}
📱 Restaurant Phone: {restaurant_phone}
📍 Delivery to: {delivery_location}

📋 ORDER ITEMS:
{items_text.strip()}

💰 Subtotal: {order_data.get('subtotal', 0)} birr"""

    return order_info

# Import order tracking notifications (avoid circular import by importing at runtime)
def get_order_tracking_notifications():
    """Get order tracking notification functions"""
    try:
        from src.bots.order_track_bot import (
            notify_delivery_accepted,
            notify_delivery_completed
        )
        return notify_delivery_accepted, notify_delivery_completed
    except ImportError:
        logger.warning("Could not import order tracking notifications")
        return None, None

# Initialize the delivery bot
delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

# ============================================================================
# DYNAMIC AUTHORIZATION SYSTEM WITH FIREBASE INTEGRATION
# ============================================================================

# Cache for authorized IDs to avoid repeated Firestore queries
_authorized_ids_cache = None
_cache_timestamp = None
_cache_duration = 300  # 5 minutes cache

def get_authorized_delivery_ids_from_firebase():
    """Get authorized delivery personnel IDs from Firebase with enhanced debugging"""
    global _authorized_ids_cache, _cache_timestamp

    try:
        current_time = time.time()

        # Check if cache is still valid
        cache_age = (current_time - _cache_timestamp) if _cache_timestamp else float('inf')
        if (_authorized_ids_cache is not None and
            _cache_timestamp is not None and
            cache_age < _cache_duration):
            logger.debug(f"Using cached authorized IDs (age: {cache_age:.1f}s): {_authorized_ids_cache}")
            return _authorized_ids_cache

        logger.info(f"🔄 Refreshing authorization cache (age: {cache_age:.1f}s)")

        # Fetch from Firebase
        from src.firebase_db import get_data
        authorized_personnel = get_data("authorized_delivery_personnel") or {}

        logger.info(f"📊 Firebase authorized_delivery_personnel collection: {len(authorized_personnel)} records")

        authorized_ids = []
        for personnel_id, person_data in authorized_personnel.items():
            telegram_id = person_data.get('telegram_id')
            status = person_data.get('status', 'unknown')
            name = person_data.get('name', 'Unknown')

            logger.info(f"   👤 {personnel_id}: {name} (ID: {telegram_id}, Status: {status})")

            if status == 'active' and telegram_id:
                authorized_ids.append(int(telegram_id))
                logger.info(f"      ✅ Added to authorized list: {telegram_id}")
            else:
                logger.info(f"      ⏭️ Skipped: status={status}, telegram_id={telegram_id}")

        # Always include admin IDs as fallback
        admin_ids = DELIVERY_BOT_AUTHORIZED_IDS
        all_ids = list(set(authorized_ids + admin_ids))

        # Update cache
        _authorized_ids_cache = all_ids
        _cache_timestamp = current_time

        logger.info(f"✅ Updated delivery bot authorized IDs cache:")
        logger.info(f"   📦 Firebase IDs: {authorized_ids}")
        logger.info(f"   🔧 Admin IDs: {admin_ids}")
        logger.info(f"   🎯 Total authorized: {all_ids}")

        return all_ids

    except Exception as e:
        logger.error(f"❌ Error getting authorized IDs from Firebase: {e}")
        import traceback
        traceback.print_exc()

        # Return cached data if available, otherwise fallback to config
        if _authorized_ids_cache is not None:
            logger.warning(f"⚠️ Using cached authorized IDs due to Firebase error: {_authorized_ids_cache}")
            return _authorized_ids_cache
        else:
            logger.warning(f"⚠️ Using config fallback for authorized IDs: {DELIVERY_BOT_AUTHORIZED_IDS}")
            return DELIVERY_BOT_AUTHORIZED_IDS

def clear_authorization_cache():
    """Clear the authorization cache to force refresh"""
    global _authorized_ids_cache, _cache_timestamp
    _authorized_ids_cache = None
    _cache_timestamp = None
    logger.info("Authorization cache cleared")

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to access the delivery bot with enhanced debugging"""
    try:
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        is_auth = user_id in authorized_ids

        logger.info(f"🔐 Authorization check for user {user_id}:")
        logger.info(f"   📋 Authorized IDs: {authorized_ids}")
        logger.info(f"   🎯 User {user_id} authorized: {is_auth}")

        if not is_auth:
            logger.warning(f"❌ Unauthorized access attempt by user ID: {user_id}")
            logger.warning(f"   Available authorized IDs: {authorized_ids}")

            # Check if user might be in Firebase but with wrong status
            try:
                from src.firebase_db import get_data
                authorized_personnel = get_data("authorized_delivery_personnel") or {}

                user_found = False
                for personnel_id, person_data in authorized_personnel.items():
                    if person_data.get('telegram_id') == user_id:
                        user_found = True
                        status = person_data.get('status', 'unknown')
                        name = person_data.get('name', 'Unknown')
                        logger.warning(f"   🔍 User {user_id} found in Firebase: {name} (status: {status})")
                        break

                if not user_found:
                    logger.warning(f"   🔍 User {user_id} not found in Firebase authorized_delivery_personnel collection")

            except Exception as debug_error:
                logger.error(f"   ❌ Error during authorization debugging: {debug_error}")
        else:
            logger.info(f"✅ User {user_id} successfully authorized")

        return is_auth

    except Exception as e:
        logger.error(f"❌ Error checking authorization for user {user_id}: {e}")
        import traceback
        traceback.print_exc()

        # Fallback to config file
        fallback_auth = user_id in DELIVERY_BOT_AUTHORIZED_IDS
        logger.warning(f"⚠️ Using config fallback for user {user_id}: {fallback_auth}")
        return fallback_auth

def access_denied_message(message):
    """Send access denied message to unauthorized users"""
    delivery_bot.reply_to(
        message,
        "🚫 Access Denied\n\nYou are not authorized to use this delivery system."
    )

def get_personnel_by_telegram_id(telegram_id: int):
    """Get delivery personnel by Telegram ID with fresh data from Firebase"""
    try:
        # Load fresh data directly from Firebase
        personnel_data = get_data("delivery_personnel") or {}

        for personnel_id, personnel_dict in personnel_data.items():
            if personnel_dict.get('telegram_id') == str(telegram_id):
                # Convert dict back to DeliveryPersonnel object using from_dict method
                from src.data_models import DeliveryPersonnel
                personnel = DeliveryPersonnel.from_dict(personnel_dict)
                return personnel

        return None

    except Exception as e:
        logger.error(f"Error getting personnel by Telegram ID {telegram_id}: {e}")
        return None

@delivery_bot.message_handler(commands=['start'])
def start_command(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    # Check if user is registered as delivery personnel
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel. Please contact admin."
        )
        return
    
    welcome_text = f"""
🚚 **Delivery System**
Welcome {personnel.name}!

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status
📍 /location - Update your location

**Order Statuses:**
• `assigned` - Order assigned to you
• `accepted` - You accepted the order
• `picked_up` - Order picked up from restaurant
• `delivered` - Order delivered to customer
• `cancelled` - Order cancelled
    """
    
    delivery_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@delivery_bot.message_handler(commands=['orders'])
def view_available_orders_command(message):
    """View available orders for delivery"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Load confirmed orders from Firebase
        confirmed_orders = get_data("confirmed_orders") or {}

        if not confirmed_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for delivery."
            )
            return
        
        # Filter orders that are assigned to this personnel or unassigned
        available_orders = []
        
        for order_number, order_data in confirmed_orders.items():
            assignment = get_delivery_assignment_by_order(order_number)
            
            # Show unassigned orders or orders assigned to this personnel
            if not assignment or assignment.get('personnel_id') == personnel.personnel_id:
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                # Format order items for delivery personnel (limited info)
                items_text = ""
                if order_data.get('items'):
                    for item in order_data['items']:
                        items_text += f"• {item.get('name', 'Unknown')} (x{item.get('quantity', 1)}) - {item.get('price', 0)} birr\n"
                
                # Use privacy-protected format for delivery bot
                order_info = format_privacy_protected_order_info(
                    order_number, order_data, restaurant_name, items_text
                )
                
                available_orders.append((order_number, order_info.strip()))
        
        if not available_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for you at the moment."
            )
            return
        
        # Send orders with accept/decline buttons
        for order_number, order_info in available_orders[:5]:  # Limit to 5 orders
            # Create inline keyboard for accept/decline
            markup = types.InlineKeyboardMarkup()
            accept_btn = types.InlineKeyboardButton("✅ Accept", callback_data=f"accept_{order_number}")
            decline_btn = types.InlineKeyboardButton("❌ Decline", callback_data=f"decline_{order_number}")
            markup.row(accept_btn, decline_btn)
            
            delivery_bot.send_message(
                message.chat.id,
                order_info,
                parse_mode='Markdown',
                reply_markup=markup
            )
                
    except Exception as e:
        logger.error(f"Error in view_available_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving orders. Please try again later."
        )

@delivery_bot.callback_query_handler(func=lambda call: call.data.startswith(('accept_order_', 'decline_order_', 'accept_', 'decline_', 'complete_order_')))
def handle_order_decision(call):
    """Handle order accept/decline decisions with enhanced authorization debugging"""
    user_id = call.from_user.id
    callback_data = call.data

    logger.info(f"🔘 Callback query received from user {user_id}: {callback_data}")

    # Enhanced authorization check with detailed logging
    if not is_authorized(user_id):
        logger.warning(f"❌ Callback authorization failed for user {user_id} on action: {callback_data}")
        delivery_bot.answer_callback_query(call.id, "🚫 Access Denied - You are not authorized to use this delivery system")
        return

    logger.info(f"✅ Callback authorization successful for user {user_id}")

    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.answer_callback_query(call.id, "Not registered as delivery personnel")
        return

    try:
        # Handle both new broadcast format (accept_order_/decline_order_) and old format (accept_/decline_)
        if call.data.startswith('accept_order_'):
            action = "accept"
            order_number = call.data.replace('accept_order_', '')
        elif call.data.startswith('decline_order_'):
            action = "decline"
            order_number = call.data.replace('decline_order_', '')
        elif call.data.startswith('complete_order_'):
            action = "complete"
            order_number = call.data.replace('complete_order_', '')
        else:
            # Old format
            action, order_number = call.data.split('_', 1)

        if action == "accept":
            # For broadcast orders, we need to assign first, then accept
            from src.utils.delivery_personnel_utils import assign_order_to_personnel
            from src.firebase_db import get_data, set_data

            # Check if order is still available (not already assigned)
            confirmed_orders = get_data("confirmed_orders") or {}
            if order_number not in confirmed_orders:
                delivery_bot.answer_callback_query(call.id, "❌ Order no longer available")
                return

            order_data = confirmed_orders[order_number]
            if order_data.get('delivery_status') != 'pending_assignment':
                delivery_bot.answer_callback_query(call.id, "❌ Order already assigned to another delivery person")
                return

            # Enhanced capacity check with real-time validation
            from src.utils.delivery_personnel_utils import get_real_time_capacity
            current_capacity = get_real_time_capacity(personnel.personnel_id)
            max_allowed_capacity = min(personnel.max_capacity, 5)  # Enforce 5-order limit

            if current_capacity >= max_allowed_capacity:
                delivery_bot.answer_callback_query(
                    call.id,
                    f"❌ You are at maximum capacity ({current_capacity}/{max_allowed_capacity} orders). Complete existing orders first."
                )
                logger.info(f"Personnel {personnel.personnel_id} rejected order {order_number} due to capacity limit ({current_capacity}/{max_allowed_capacity})")
                return

            # Assign the order to this personnel
            delivery_fee = order_data.get('delivery_fee', 0)
            assignment_id = assign_order_to_personnel(order_number, personnel.personnel_id, delivery_fee)

            if assignment_id:
                # Update order status in Firebase
                order_data['delivery_status'] = 'assigned'
                order_data['assigned_to'] = personnel.personnel_id
                order_data['assigned_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                set_data(f"confirmed_orders/{order_number}", order_data)

                delivery_bot.answer_callback_query(call.id, "✅ Order accepted!")

                # Create "Complete Order" button for the accepted order
                complete_markup = types.InlineKeyboardMarkup()
                complete_btn = types.InlineKeyboardButton(
                    "🏁 Complete Order",
                    callback_data=f"complete_order_{order_number}"
                )
                complete_markup.add(complete_btn)

                # Update message with Complete Order button and detailed order information
                try:
                    logger.info(f"🔄 Updating message for order {order_number} with Complete Order button")

                    # Get restaurant name
                    restaurant_name = order_data.get('restaurant', 'Unknown Restaurant')

                    # Format items list
                    items = order_data.get('items', [])
                    items_text = ""
                    for item in items:
                        items_text += f"• {item.get('name', 'Unknown Item')} - {item.get('price', 0)} birr\n"

                    # Get delivery details with privacy protection
                    delivery_location = format_delivery_location_for_bot(
                        order_data.get('delivery_location', 'N/A'),
                        order_data.get('delivery_gate')
                    )
                    restaurant_phone = get_restaurant_phone_for_delivery()
                    subtotal = order_data.get('subtotal', 0)  # Hide delivery fee for privacy

                    # Create privacy-protected acceptance message
                    new_message_text = f"""✅ **ORDER #{order_number} ACCEPTED**

📋 **Order Details:**
🏪 Restaurant: {restaurant_name}
📱 Restaurant Phone: {restaurant_phone}
📍 Delivery to: {delivery_location}

🛍️ **Items:**
{items_text.strip()}

💰 **Subtotal:** {subtotal} birr

📦 **Status:** Assigned to you
⏰ **Accepted at:** {order_data['assigned_at']}

Click 'Complete Order' when delivery is finished."""

                    delivery_bot.edit_message_text(
                        new_message_text,
                        call.message.chat.id,
                        call.message.message_id,
                        reply_markup=complete_markup,
                        parse_mode='Markdown'
                    )

                    logger.info(f"✅ Successfully updated message with detailed order info for order {order_number}")

                except Exception as edit_error:
                    logger.error(f"❌ Failed to update message with Complete Order button: {edit_error}")
                    logger.error(f"   Error type: {type(edit_error).__name__}")
                    logger.error(f"   Error details: {str(edit_error)}")

                    # Try sending a new message instead of editing
                    try:
                        logger.info(f"🔄 Attempting to send new message with Complete Order button for order {order_number}")
                        delivery_bot.send_message(
                            call.message.chat.id,
                            f"✅ Order #{order_number} ACCEPTED\n\nOrder assigned to you\nClick 'Complete Order' when delivery is finished",
                            reply_markup=complete_markup
                        )
                        logger.info(f"✅ Sent new message with Complete Order button for order {order_number}")
                    except Exception as send_error:
                        logger.error(f"❌ Failed to send new message: {send_error}")
                        logger.error(f"   Send error type: {type(send_error).__name__}")
                        logger.error(f"   Send error details: {str(send_error)}")

                        # Last resort: just send a simple notification
                        try:
                            delivery_bot.send_message(
                                call.message.chat.id,
                                f"Order #{order_number} accepted! Please complete delivery and notify when finished."
                            )
                        except:
                            logger.error(f"❌ Complete failure to send any message for order {order_number}")

                # Notify order tracking bot with delivery person details
                try:
                    notify_delivery_accepted, _ = get_order_tracking_notifications()
                    if notify_delivery_accepted:
                        notify_delivery_accepted(order_number, personnel.name)
                except Exception as e:
                    logger.error(f"Failed to send acceptance notification: {e}")

                # Clean up original order broadcast messages from other delivery personnel
                try:
                    from src.firebase_db import get_data, delete_data
                    from src.config import DELIVERY_BOT_TOKEN
                    import telebot

                    cleanup_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

                    # Get stored broadcast message IDs for this order
                    broadcast_messages = get_data(f"order_broadcast_messages/{order_number}")

                    if broadcast_messages:
                        logger.info(f"🧹 Starting message cleanup for order #{order_number} - found {len(broadcast_messages)} broadcast messages")

                        cleanup_count = 0
                        cleanup_failed = 0

                        for other_personnel_id, message_data in broadcast_messages.items():
                            # Skip the personnel who accepted the order
                            if other_personnel_id == personnel.personnel_id:
                                continue

                            try:
                                telegram_id = message_data.get('telegram_id')
                                message_id = message_data.get('message_id')

                                if telegram_id and message_id:
                                    # Delete the original order broadcast message
                                    cleanup_bot.delete_message(telegram_id, message_id)
                                    cleanup_count += 1
                                    logger.info(f"🗑️  Deleted order broadcast message for personnel {other_personnel_id} (Telegram: {telegram_id}, Message: {message_id})")
                                else:
                                    logger.warning(f"⚠️  Missing telegram_id or message_id for personnel {other_personnel_id}")
                                    cleanup_failed += 1

                            except Exception as cleanup_error:
                                cleanup_failed += 1
                                error_message = str(cleanup_error).lower()
                                if "message to delete not found" in error_message:
                                    logger.info(f"ℹ️  Message already deleted for personnel {other_personnel_id}")
                                else:
                                    logger.warning(f"⚠️  Failed to delete message for personnel {other_personnel_id}: {cleanup_error}")

                        # Clean up the broadcast message data from Firebase
                        try:
                            delete_data(f"order_broadcast_messages/{order_number}")
                            logger.info(f"🗑️  Cleaned up broadcast message data for order #{order_number}")
                        except Exception as delete_error:
                            logger.warning(f"⚠️  Failed to delete broadcast message data: {delete_error}")

                        logger.info(f"✅ Message cleanup completed for order #{order_number}: {cleanup_count} deleted, {cleanup_failed} failed")
                    else:
                        logger.info(f"ℹ️  No broadcast messages found for cleanup for order #{order_number}")

                except Exception as cleanup_error:
                    logger.error(f"❌ Failed to clean up broadcast messages for order #{order_number}: {cleanup_error}")

            else:
                delivery_bot.answer_callback_query(call.id, "❌ Failed to accept order - may have been taken by another delivery person")

        elif action == "decline":
            # Simply decline - no assignment needed
            delivery_bot.answer_callback_query(call.id, "❌ Order declined")
            delivery_bot.edit_message_text(
                f"❌ **Order #{order_number} DECLINED**\n\n{call.message.text}",
                call.message.chat.id,
                call.message.message_id,
                parse_mode='Markdown'
            )

        elif action == "complete":
            # Handle delivery completion
            from src.firebase_db import get_data, set_data

            # Verify this personnel is assigned to this order
            confirmed_orders = get_data("confirmed_orders") or {}
            if order_number not in confirmed_orders:
                delivery_bot.answer_callback_query(call.id, "❌ Order not found")
                return

            order_data = confirmed_orders[order_number]
            if order_data.get('assigned_to') != personnel.personnel_id:
                delivery_bot.answer_callback_query(call.id, "❌ You are not assigned to this order")
                return

            if order_data.get('delivery_status') == 'completed':
                delivery_bot.answer_callback_query(call.id, "❌ Order already marked as completed")
                return

            # Update order status to completed
            order_data['delivery_status'] = 'completed'
            order_data['completed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            order_data['completed_by'] = personnel.personnel_id
            set_data(f"confirmed_orders/{order_number}", order_data)

            # Update delivery assignment status
            from src.utils.delivery_personnel_utils import (
                update_assignment_status,
                update_personnel_capacity_on_assignment
            )
            update_assignment_status(order_number, personnel.personnel_id, "delivered")

            # Update enhanced capacity tracking when order is completed
            capacity_updated = update_personnel_capacity_on_assignment(
                personnel.personnel_id, order_number, 'complete'
            )
            if capacity_updated:
                logger.info(f"Updated capacity tracking for {personnel.personnel_id} after completing order {order_number}")
            else:
                logger.warning(f"Failed to update capacity tracking for {personnel.personnel_id}")

            # Update earnings for the delivery personnel with validation
            delivery_fee = order_data.get('delivery_fee', 0)
            if delivery_fee > 0 and isinstance(delivery_fee, (int, float)):
                from src.utils.earnings_utils import update_personnel_earnings
                earnings_updated = update_personnel_earnings(personnel.personnel_id, delivery_fee)
                if earnings_updated:
                    logger.info(f"Updated earnings for personnel {personnel.personnel_id}: +{delivery_fee} birr")
                else:
                    logger.error(f"Failed to update earnings for personnel {personnel.personnel_id}")
            elif delivery_fee <= 0:
                logger.warning(f"Invalid delivery fee for order {order_number}: {delivery_fee}")
            else:
                logger.error(f"Invalid delivery fee type for order {order_number}: {type(delivery_fee)}")

            delivery_bot.answer_callback_query(call.id, "✅ Order marked as completed!")

            # Create detailed completion message with complete order information
            try:
                # Get restaurant name
                restaurant_name = order_data.get('restaurant', 'Unknown Restaurant')

                # Format items list
                items = order_data.get('items', [])
                items_text = ""
                for item in items:
                    items_text += f"• {item.get('name', 'Unknown Item')} - {item.get('price', 0)} birr\n"

                # Get delivery details with privacy protection
                delivery_location = format_delivery_location_for_bot(
                    order_data.get('delivery_location', 'N/A'),
                    order_data.get('delivery_gate')
                )
                restaurant_phone = get_restaurant_phone_for_delivery()
                subtotal = order_data.get('subtotal', 0)  # Hide delivery fee for privacy

                # Create privacy-protected completion message
                completion_message = f"""✅ **ORDER #{order_number} COMPLETED**

📋 **Order Details:**
🏪 Restaurant: {restaurant_name}
📱 Restaurant Phone: {restaurant_phone}
📍 Delivered to: {delivery_location}

🛍️ **Items Delivered:**
{items_text.strip()}

💰 **Subtotal:** {subtotal} birr

📦 **Status:** Delivery completed
⏰ **Completed at:** {order_data['completed_at']}

🔔 Customer will now receive confirmation request."""

                delivery_bot.edit_message_text(
                    completion_message,
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown'
                )

                logger.info(f"✅ Successfully updated completion message with detailed order info for order {order_number}")

            except Exception as edit_error:
                logger.error(f"❌ Failed to update completion message: {edit_error}")
                # Fallback to simple message
                delivery_bot.edit_message_text(
                    f"✅ **Order #{order_number} COMPLETED**\n\n📋 **Status**: Delivery completed\n⏰ **Completed at**: {order_data['completed_at']}\n\n🔔 Customer will now receive confirmation request.",
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown'
                )

            # Notify order tracking bot about delivery completion
            try:
                _, notify_delivery_completed = get_order_tracking_notifications()
                if notify_delivery_completed:
                    notify_delivery_completed(order_number, personnel.name)
                    logger.info(f"Sent delivery completion notification for order {order_number}")
            except Exception as e:
                logger.error(f"Failed to send delivery completion notification: {e}")

    except Exception as e:
        logger.error(f"Error in handle_order_decision: {e}")
        delivery_bot.answer_callback_query(call.id, "❌ Error processing request")

@delivery_bot.message_handler(commands=['myorders'])
def view_my_orders_command(message):
    """View orders assigned to this delivery personnel"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Get assignments for this personnel
        assignments = get_assignments_for_personnel(personnel.personnel_id)
        
        if not assignments:
            delivery_bot.reply_to(
                message,
                "📭 You have no assigned orders."
            )
            return
        
        # Load order data
        awaiting_orders = load_awaiting_receipt()
        
        my_orders = []
        for assignment in assignments:
            order_number = assignment.get('order_number')
            if order_number in awaiting_orders:
                order_data = awaiting_orders[order_number]
                
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                # Use privacy-protected format for my orders
                delivery_location = format_delivery_location_for_bot(
                    order_data.get('delivery_location', 'N/A'),
                    order_data.get('delivery_gate')
                )
                restaurant_phone = get_restaurant_phone_for_delivery()

                order_info = f"""📋 **Order #{order_number}**
🏪 Restaurant: {restaurant_name}
📱 Restaurant Phone: {restaurant_phone}
📍 Delivery to: {delivery_location}
💰 Subtotal: {order_data.get('subtotal', 0)} birr
📊 Status: {assignment.get('status', 'Unknown').title()}
⏰ Assigned: {assignment.get('assigned_at', 'N/A')}"""
                my_orders.append(order_info.strip())
        
        if my_orders:
            response = "🚚 **Your Assigned Orders:**\n\n" + "\n\n".join(my_orders)
            delivery_bot.reply_to(message, response, parse_mode='Markdown')
        else:
            delivery_bot.reply_to(
                message,
                "📭 You have no current orders."
            )
                
    except Exception as e:
        logger.error(f"Error in view_my_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving your orders. Please try again later."
        )

@delivery_bot.message_handler(commands=['status'])
def update_order_status_command(message):
    """Update order status"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Parse command: /status ORDER_NUMBER STATUS
        command_parts = message.text.split()
        if len(command_parts) < 3:
            delivery_bot.reply_to(
                message,
                "❌ Usage: /status ORDER_NUMBER STATUS\n\nValid statuses: accepted, picked_up, delivered, cancelled"
            )
            return
        
        order_number = command_parts[1]
        new_status = command_parts[2].lower()
        
        valid_statuses = ['accepted', 'picked_up', 'delivered', 'cancelled']
        if new_status not in valid_statuses:
            delivery_bot.reply_to(
                message,
                f"❌ Invalid status. Valid statuses: {', '.join(valid_statuses)}"
            )
            return
        
        # Update the status
        success = update_assignment_status(order_number, personnel.personnel_id, new_status)
        if success:
            delivery_bot.reply_to(
                message,
                f"✅ Order #{order_number} status updated to: {new_status.title()}"
            )

            # Notify order tracking bot if order is marked as delivered
            if new_status == "delivered":
                try:
                    # Update earnings for delivery completion with validation
                    from src.firebase_db import get_data
                    confirmed_orders = get_data("confirmed_orders") or {}
                    if order_number in confirmed_orders:
                        order_data = confirmed_orders[order_number]
                        delivery_fee = order_data.get('delivery_fee', 0)
                        if delivery_fee > 0 and isinstance(delivery_fee, (int, float)):
                            from src.utils.earnings_utils import update_personnel_earnings
                            earnings_updated = update_personnel_earnings(personnel.personnel_id, delivery_fee)
                            if earnings_updated:
                                logger.info(f"Updated earnings for personnel {personnel.personnel_id}: +{delivery_fee} birr (via status update)")
                            else:
                                logger.error(f"Failed to update earnings for personnel {personnel.personnel_id} (via status update)")
                        elif delivery_fee <= 0:
                            logger.warning(f"Invalid delivery fee for order {order_number} (via status update): {delivery_fee}")
                        else:
                            logger.error(f"Invalid delivery fee type for order {order_number} (via status update): {type(delivery_fee)}")
                    else:
                        logger.warning(f"Order {order_number} not found in confirmed orders for earnings update")

                    # Notify order tracking bot
                    _, notify_delivery_completed = get_order_tracking_notifications()
                    if notify_delivery_completed:
                        notify_delivery_completed(order_number, personnel.name)
                except Exception as e:
                    logger.error(f"Failed to send delivery completion notification: {e}")

        else:
            delivery_bot.reply_to(
                message,
                f"❌ Failed to update order #{order_number} status. Order may not be assigned to you."
            )
                
    except Exception as e:
        logger.error(f"Error in update_order_status_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error updating order status. Please try again later."
        )

@delivery_bot.message_handler(func=lambda message: True)
def handle_unknown_command(message):
    """Handle unknown commands"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    help_text = """
❓ **Unknown Command**

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status

Example: `/status ORD_20241230_001 picked_up`
    """
    
    delivery_bot.reply_to(message, help_text, parse_mode='Markdown')

def run_delivery_bot():
    """Run the delivery bot"""
    logger.info("Starting Delivery Bot...")
    try:
        delivery_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Delivery Bot error: {e}")
        raise

if __name__ == "__main__":
    run_delivery_bot()
